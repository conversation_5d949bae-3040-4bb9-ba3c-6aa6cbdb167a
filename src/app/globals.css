@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
    Arial, sans-serif;
}

body {
  background-color: #f0f2f5;
  color: #000000d9;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Ant Design 样式覆盖 */
.ant-layout-sider {
  background: #001529 !important;
}

.ant-menu-dark {
  background: #001529 !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.ant-layout-header {
  background: #fff !important;
  padding: 0 24px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.ant-table-thead > tr > th {
  background: #fafafa !important;
  color: #000000d9 !important;
  font-weight: 600;
}

.ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 自定义工具类 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shadow-ant {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.shadow-ant-hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 移动端专用工具类 */
.mobile-full-width {
  width: 100% !important;
}

.mobile-text-center {
  text-align: center !important;
}

.mobile-hidden {
  display: none !important;
}

.mobile-block {
  display: block !important;
}

.mobile-flex {
  display: flex !important;
}

.mobile-flex-col {
  flex-direction: column !important;
}

.mobile-space-y-2 > * + * {
  margin-top: 8px !important;
}

.mobile-space-y-4 > * + * {
  margin-top: 16px !important;
}

.mobile-p-2 {
  padding: 8px !important;
}

.mobile-p-4 {
  padding: 16px !important;
}

.mobile-m-2 {
  margin: 8px !important;
}

.mobile-m-4 {
  margin: 16px !important;
}

/* 触摸友好的按钮 */
.touch-target {
  min-height: 44px !important;
  min-width: 44px !important;
}

/* 移动端卡片优化 */
.mobile-card {
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 12px !important;
}

/* 移动端表格卡片视图 */
.mobile-table-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
}

.mobile-table-card-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #262626;
}

.mobile-table-card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mobile-table-card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #f5f5f5;
}

.mobile-table-card-row:last-child {
  border-bottom: none;
}

.mobile-table-card-label {
  font-size: 12px;
  color: #8c8c8c;
  flex-shrink: 0;
  margin-right: 12px;
}

.mobile-table-card-value {
  font-size: 14px;
  color: #262626;
  text-align: right;
  word-break: break-all;
}

/* 移动端表格专用类 */
.mobile-table {
  width: 100% !important;
}

.mobile-table .ant-table-wrapper {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

.mobile-table .ant-table-container {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

.mobile-table .ant-table-body {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 响应式隐藏类 */
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

/* 响应式布局 */
@media (max-width: 576px) {
  .ant-layout-content {
    margin: 8px !important;
    padding: 12px !important;
  }

  .ant-layout-header {
    padding: 0 12px !important;
    height: 56px !important;
  }

  .ant-breadcrumb {
    font-size: 12px !important;
  }

  .ant-card {
    margin-bottom: 12px;
  }

  .ant-col {
    margin-bottom: 12px;
  }

  /* 移动端表格优化 */
  .ant-table-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .ant-table {
    min-width: 100% !important;
  }

  .ant-table-container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .ant-table-body {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }

  /* 移动端表单优化 */
  .ant-form-item {
    margin-bottom: 12px !important;
  }

  .ant-form-item-label {
    padding-bottom: 4px !important;
  }

  .ant-input, .ant-select-selector {
    height: 40px !important;
    font-size: 16px !important; /* 防止iOS缩放 */
  }

  .ant-btn {
    height: 40px !important;
    font-size: 14px !important;
  }

  /* 移动端模态框优化 */
  .ant-modal {
    margin: 0 !important;
    max-width: 100vw !important;
    top: 0 !important;
    padding-bottom: 0 !important;
  }

  .ant-modal-content {
    border-radius: 0 !important;
    min-height: 100vh !important;
  }

  .ant-modal-header {
    padding: 16px !important;
    border-radius: 0 !important;
  }

  .ant-modal-body {
    padding: 16px !important;
    max-height: calc(100vh - 120px) !important;
    overflow-y: auto !important;
  }

  .ant-modal-footer {
    padding: 12px 16px !important;
    border-radius: 0 !important;
  }

  .ant-modal-footer .ant-btn {
    margin-left: 8px !important;
  }
}

@media (max-width: 768px) {
  .ant-layout-content {
    margin: 12px !important;
    padding: 16px !important;
  }

  .ant-layout-header {
    padding: 0 16px !important;
    height: 64px !important;
  }

  .ant-table-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .ant-table-container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .ant-table-body {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .ant-form-item {
    margin-bottom: 16px !important;
  }

  /* 中等屏幕表格优化 */
  .ant-table-thead > tr > th {
    padding: 12px 8px !important;
  }

  .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
  }

  /* 中等屏幕模态框优化 */
  .ant-modal {
    margin: 20px !important;
    max-width: calc(100vw - 40px) !important;
  }

  .ant-modal-content {
    border-radius: 8px !important;
  }
}

@media (max-width: 992px) {
  .ant-layout-sider-collapsed {
    flex: 0 0 0 !important;
    min-width: 0 !important;
    width: 0 !important;
  }
}

/* 移动端菜单优化 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh !important;
    z-index: 1001 !important;
    left: 0 !important;
    top: 0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15) !important;
  }

  .ant-layout-sider-collapsed {
    left: -256px !important;
  }

  .mobile-sider-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1000;
    display: none;
    transition: opacity 0.3s ease;
  }

  .mobile-sider-mask.visible {
    display: block;
  }

  /* 移动端菜单项优化 */
  .ant-menu-item {
    height: 48px !important;
    line-height: 48px !important;
    margin: 4px 0 !important;
  }

  .ant-menu-submenu-title {
    height: 48px !important;
    line-height: 48px !important;
    margin: 4px 0 !important;
  }

  .ant-menu-item-icon {
    font-size: 18px !important;
  }

  /* 移动端主内容区域调整 */
  .ant-layout {
    margin-left: 0 !important;
  }

  .ant-layout-content {
    margin-left: 0 !important;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 移动端专用样式增强 */
@media (max-width: 576px) {
  /* 移动端按钮组优化 */
  .ant-btn-group {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
  }

  .ant-btn-group .ant-btn {
    margin-bottom: 8px !important;
    border-radius: 6px !important;
  }

  .ant-btn-group .ant-btn:last-child {
    margin-bottom: 0 !important;
  }

  /* 移动端下拉菜单优化 */
  .ant-dropdown-menu {
    max-height: 60vh !important;
    overflow-y: auto !important;
  }

  .ant-dropdown-menu-item {
    padding: 12px 16px !important;
    font-size: 16px !important;
  }

  /* 移动端分页优化 */
  .ant-pagination {
    text-align: center !important;
  }

  .ant-pagination-item {
    margin: 0 2px !important;
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    margin: 0 4px !important;
  }

  /* 移动端步骤条优化 */
  .ant-steps {
    margin-bottom: 24px !important;
  }

  .ant-steps-item-title {
    font-size: 12px !important;
  }

  .ant-steps-item-description {
    font-size: 11px !important;
  }

  /* 移动端标签页优化 */
  .ant-tabs-tab {
    padding: 8px 12px !important;
    font-size: 14px !important;
  }

  .ant-tabs-content-holder {
    padding: 16px 0 !important;
  }

  /* 移动端抽屉优化 */
  .ant-drawer-body {
    padding: 16px !important;
  }

  .ant-drawer-header {
    padding: 16px !important;
  }

  /* 移动端消息提示优化 */
  .ant-message {
    top: 60px !important;
  }

  .ant-notification {
    margin-right: 8px !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .ant-btn:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  .ant-card:active {
    transform: translateY(1px) !important;
    transition: transform 0.1s ease !important;
  }

  .touch-target {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
  }
}

/* 强制移动端表格滚动修复 */
@media (max-width: 768px) {
  /* 确保表格容器可以滚动 */
  .ant-card .ant-table-wrapper,
  .mobile-card .ant-table-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
    width: 100% !important;
    max-width: 100% !important;
    position: relative !important;
  }

  /* 确保表格本身的宽度设置正确 */
  .ant-table {
    min-width: max-content !important;
    width: auto !important;
  }

  /* 修复表格容器的滚动 */
  .ant-table-container {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* 确保表格主体可以滚动 */
  .ant-table-body {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* 修复表格头部和主体的宽度同步 */
  .ant-table-thead,
  .ant-table-tbody {
    width: 100% !important;
    min-width: max-content !important;
  }

  /* 确保表格行可以正常显示 */
  .ant-table-thead > tr,
  .ant-table-tbody > tr {
    width: 100% !important;
    min-width: max-content !important;
  }

  /* 设置表格列的最小宽度 */
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    min-width: 80px !important;
    white-space: nowrap !important;
  }

  /* 修复可能的CSS冲突 */
  .ant-table-wrapper .ant-spin-nested-loading {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .ant-table-wrapper .ant-spin-container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
}

/* iOS Safari 特殊修复 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    .ant-table-wrapper {
      -webkit-overflow-scrolling: touch !important;
      overflow-x: auto !important;
      overflow-y: visible !important;
      transform: translateZ(0) !important; /* 启用硬件加速 */
    }

    .ant-table-container {
      -webkit-overflow-scrolling: touch !important;
      transform: translateZ(0) !important;
    }

    .ant-table-body {
      -webkit-overflow-scrolling: touch !important;
      transform: translateZ(0) !important;
    }
  }
}

/* Android 浏览器特殊修复 */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 1) {
  .ant-table-wrapper {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
    will-change: scroll-position !important;
  }
}

/* 移动端表格滚动指示器 */
@media (max-width: 768px) {
  .mobile-table .ant-table-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .mobile-table .ant-table-wrapper:not(.scrolled-to-right)::after {
    opacity: 1;
  }

  /* 左侧滚动指示器 */
  .mobile-table .ant-table-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.8), transparent);
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .mobile-table .ant-table-wrapper.scrolled::before {
    opacity: 1;
  }

  /* 表格滚动提示文字 */
  .mobile-table-hint {
    font-size: 12px;
    color: #8c8c8c;
    text-align: center;
    padding: 8px 0;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
  }

  .mobile-table-hint::before {
    content: '← 左右滑动查看更多列 →';
  }
}
