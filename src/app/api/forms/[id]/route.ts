import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { updateTableSchema, FieldMapping } from '@/lib/dynamicTable'

// 更新表单配置验证schema
const updateFormConfigSchema = z.object({
  formName: z
    .string()
    .min(1, '表单名称不能为空')
    .max(200, '表单名称不能超过200个字符')
    .optional(),
  fieldMapping: z
    .record(
      z.object({
        name: z.string(),
        type: z.enum([
          'string',
          'number',
          'boolean',
          'date',
          'array',
          'object',
          'text',
        ]),
        required: z.boolean(),
        enabled: z.boolean().optional(), // 新增：字段是否启用
        description: z.string().optional(),
      })
    )
    .optional(),
  isActive: z.boolean().optional(),
  fieldCount: z.number().optional(),
})

// GET /api/forms/[id] - 获取单个表单配置详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id

    const formConfig = await prisma.formConfig.findUnique({
      where: { formId },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            doctor_name: true,
          },
        },
      },
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: formConfig,
    })
  } catch (error) {
    console.error('获取表单配置详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取表单配置详情失败' },
      { status: 500 }
    )
  }
}

// PUT /api/forms/[id] - 更新表单配置
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id
    const body = await request.json()

    // 验证请求数据
    const validation = updateFormConfigSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const updateData = validation.data

    // 检查表单配置是否存在
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (!existingForm) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    // 如果更新了字段映射，重新计算启用的字段数量
    if (updateData.fieldMapping) {
      updateData.fieldCount = Object.values(updateData.fieldMapping).filter(
        field => field.enabled === true
      ).length
    }

    // 如果字段映射发生变化，需要更新数据库表结构
    let schemaUpdateWarnings: string[] = []
    if (updateData.fieldMapping) {
      console.log('检测到字段映射更新，开始更新数据库表结构...')

      const oldFieldMapping = existingForm.fieldMapping as any as Record<
        string,
        FieldMapping
      >
      const newFieldMapping = updateData.fieldMapping

      const schemaUpdateResult = await updateTableSchema(
        formId,
        oldFieldMapping || {},
        newFieldMapping
      )

      if (!schemaUpdateResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: `更新数据库表结构失败: ${schemaUpdateResult.error}`,
            details: { schemaError: schemaUpdateResult.error },
          },
          { status: 500 }
        )
      }

      if (schemaUpdateResult.warnings) {
        schemaUpdateWarnings = schemaUpdateResult.warnings
        console.log('数据库表结构更新完成，包含警告信息:', schemaUpdateWarnings)
      } else {
        console.log('数据库表结构更新完成')
      }
    }

    // 更新表单配置
    const updatedForm = await prisma.formConfig.update({
      where: { formId },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id!,
        action: 'UPDATE_FORM_CONFIG',
        resource: 'FormConfig',
        resourceId: updatedForm.id.toString(),
        details: {
          formId,
          updatedFields: Object.keys(updateData),
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    const response: any = {
      success: true,
      message: '表单配置更新成功',
      data: updatedForm,
    }

    // 如果有数据库表结构更新的警告信息，包含在响应中
    if (schemaUpdateWarnings.length > 0) {
      response.warnings = schemaUpdateWarnings
      response.message += '（包含数据库结构更新）'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('更新表单配置失败:', error)
    return NextResponse.json(
      { success: false, error: '更新表单配置失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/forms/[id] - 删除表单配置
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id

    // 检查表单配置是否存在
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
    })

    if (!existingForm) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    // 软删除：标记为非活跃状态而不是物理删除
    const deletedForm = await prisma.formConfig.update({
      where: { formId },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id!,
        action: 'DELETE_FORM_CONFIG',
        resource: 'FormConfig',
        resourceId: deletedForm.id.toString(),
        details: {
          formId,
          formName: existingForm.formName,
        },
        ipAddress:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '表单配置删除成功',
    })
  } catch (error) {
    console.error('删除表单配置失败:', error)
    return NextResponse.json(
      { success: false, error: '删除表单配置失败' },
      { status: 500 }
    )
  }
}
