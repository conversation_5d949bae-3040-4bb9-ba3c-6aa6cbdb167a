import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

export const dynamic = 'force-dynamic'

// 请求体验证模式
const toggleFormSchema = z.object({
  enabled: z.boolean(),
})

// PATCH /api/forms/[id]/toggle - 切换表单启用/禁用状态
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.id
    const body = await request.json()

    // 验证请求数据
    const validation = toggleFormSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 }
      )
    }

    const { enabled } = validation.data

    // 检查表单是否存在
    const existingForm = await prisma.formConfig.findUnique({
      where: { formId },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
          },
        },
      },
    })

    if (!existingForm) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true, id: true },
    })

    // 权限检查：管理员可以切换任何表单，普通用户可以切换自己创建的表单
    const isAdmin = currentUser?.role === 'admin'
    const isOwner = existingForm.createdById === session.user.id
    
    if (!isAdmin && !isOwner) {
      return NextResponse.json(
        { success: false, error: '权限不足，无法修改此表单状态' },
        { status: 403 }
      )
    }

    // 更新表单状态
    const updatedForm = await prisma.formConfig.update({
      where: { formId },
      data: { isActive: enabled },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            doctor_name: true,
          },
        },
      },
    })

    // 记录操作日志
    await prisma.systemLog.create({
      data: {
        userId: session.user.id,
        action: enabled ? 'ENABLE_FORM' : 'DISABLE_FORM',
        resource: 'FormConfig',
        resourceId: existingForm.id.toString(),
        details: {
          formId: formId,
          formName: existingForm.formName,
          previousState: existingForm.isActive,
          newState: enabled,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        id: updatedForm.id,
        formId: updatedForm.formId,
        formName: updatedForm.formName,
        isActive: updatedForm.isActive,
        fieldCount: updatedForm.fieldCount,
        webhookUrl: updatedForm.webhookUrl,
        createdAt: updatedForm.createdAt.toISOString(),
        updatedAt: updatedForm.updatedAt.toISOString(),
        createdBy: updatedForm.createdBy,
      },
      message: `表单已${enabled ? '启用' : '禁用'}`,
    })
  } catch (error) {
    console.error('切换表单状态失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '切换表单状态失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    )
  }
}
