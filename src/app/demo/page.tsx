'use client'

import { useState } from 'react'
import { Card, Typography, Space } from 'antd'
import { FieldMappingStep } from '@/components/forms/FieldMappingStep'

const { Title, Text } = Typography

// 示例JSON数据
const sampleJson = {
  form: 'demo_form',
  form_name: '演示表单',
  entry: {
    serial_number: 12345,
    field_1: '张三',
    field_2: '男',
    field_3: '13800138000',
    field_4: ['选项1', '选项2'],
    field_5: '<EMAIL>',
    field_6: 25,
    field_7: '北京市朝阳区',
    x_field_1: '这是一个备注信息',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z',
    creator_name: '系统用户'
  }
}

export default function DemoPage() {
  const [fieldMapping, setFieldMapping] = useState<Record<string, any> | null>(null)

  const handleFieldMappingSubmit = (mapping: Record<string, any>) => {
    setFieldMapping(mapping)
    console.log('字段映射配置:', mapping)
  }

  // 调试信息
  console.log('演示页面 - sampleJson:', sampleJson)
  console.log('演示页面 - entry字段:', Object.keys(sampleJson.entry))

  // 测试字段提取逻辑
  const testExtractFields = (entry: any) => {
    const fieldList: any[] = []
    Object.keys(entry).forEach(key => {
      // 跳过系统字段
      if (
        key === 'serial_number' ||
        key.startsWith('info_') ||
        key === 'created_at' ||
        key === 'updated_at' ||
        key === 'creator_name' ||
        key === 'color_mark'
      ) {
        return
      }
      fieldList.push(key)
    })
    return fieldList
  }

  const extractedFieldKeys = testExtractFields(sampleJson.entry)
  console.log('演示页面 - 提取的字段:', extractedFieldKeys)

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <Card className="mb-6">
          <Title level={2}>字段选择功能演示</Title>
          <Text type="secondary">
            这个演示页面展示了新的字段选择功能。您可以选择需要接收的字段，未选择的字段将被忽略。
          </Text>
        </Card>

        <FieldMappingStep
          sampleJson={sampleJson}
          onSubmit={handleFieldMappingSubmit}
        />

        {fieldMapping && (
          <Card className="mt-6">
            <Title level={4}>配置结果</Title>
            <Space direction="vertical" className="w-full">
              <Text strong>启用的字段:</Text>
              {Object.entries(fieldMapping)
                .filter(([_, config]: [string, any]) => config.enabled === true)
                .map(([key, config]: [string, any]) => (
                  <div key={key} className="p-2 bg-green-50 rounded">
                    <Text code>{key}</Text>: {config.name} ({config.type})
                    {config.required && <Text type="danger"> *必填</Text>}
                  </div>
                ))}
              
              <Text strong className="mt-4">未启用的字段:</Text>
              {Object.entries(fieldMapping)
                .filter(([_, config]: [string, any]) => config.enabled === false)
                .map(([key, config]: [string, any]) => (
                  <div key={key} className="p-2 bg-gray-100 rounded">
                    <Text code>{key}</Text>: {config.name} ({config.type}) - 
                    <Text type="secondary"> 已禁用</Text>
                  </div>
                ))}
            </Space>
          </Card>
        )}
      </div>
    </div>
  )
}
