'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Table,
  Input,
  Select,
  DatePicker,
  Space,
  Tag,
  Button,
  Modal,
  Descriptions,
  Alert,
  Statistic,
  Row,
  Col,
  message,
  Popconfirm,
  Drawer,
  Form,
  Tooltip,
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FilterOutlined,
  ClearOutlined,
  Exclamation<PERSON>ircleOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  Bar<PERSON>hartOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker
const { TextArea } = Input

interface ValidationFailure {
  id: string
  formId: string
  originalData: any
  validationErrors: any
  errorType: string
  errorMessage: string
  expectedFormat?: any
  suggestions?: any
  status: 'pending' | 'reviewing' | 'resolved' | 'ignored'
  resolvedBy?: number
  resolvedAt?: string
  notes?: string
  ipAddress: string
  userAgent: string
  createdAt: string
  updatedAt: string
  resolver?: {
    id: number
    username: string
    nickname: string
  }
}

interface ValidationFailureStats {
  summary: {
    total: number
    today: number
    pending: number
    resolved: number
    resolution_rate: string
  }
  errorTypeStats: Array<{
    errorType: string
    count: number
    percentage: string
  }>
  formIdStats: Array<{
    formId: string
    count: number
    percentage: string
  }>
  statusStats: Array<{
    status: string
    count: number
    percentage: string
  }>
  recentTrends: Array<{
    date: string
    count: number
  }>
  resolutionTime: {
    avg_hours: number | null
    min_hours: number | null
    max_hours: number | null
  }
}

interface ValidationFailureListResponse {
  success: boolean
  data: {
    records: ValidationFailure[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

export default function ValidationFailuresPage() {
  const [records, setRecords] = useState<ValidationFailure[]>([])
  const [stats, setStats] = useState<ValidationFailureStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedRecord, setSelectedRecord] =
    useState<ValidationFailure | null>(null)
  const [detailVisible, setDetailVisible] = useState(false)
  const [statusDrawerVisible, setStatusDrawerVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [statsVisible, setStatsVisible] = useState(false)
  const [form] = Form.useForm()

  const [filters, setFilters] = useState({
    formId: '',
    errorType: '',
    status: '',
    dateRange: null as any,
  })

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  })

  // 错误类型映射
  const errorTypeMap: Record<
    string,
    { label: string; color: string; icon: React.ReactNode }
  > = {
    SCHEMA_VALIDATION: {
      label: 'Schema验证',
      color: 'red',
      icon: <ExclamationCircleOutlined />,
    },
    FORM_ID_MISMATCH: {
      label: '表单ID不匹配',
      color: 'orange',
      icon: <CloseCircleOutlined />,
    },
    FORM_CONFIG_NOT_FOUND: {
      label: '表单配置不存在',
      color: 'volcano',
      icon: <ExclamationCircleOutlined />,
    },
    TABLE_NAME_NOT_CONFIGURED: {
      label: '表名未配置',
      color: 'magenta',
      icon: <ExclamationCircleOutlined />,
    },
    DATA_INSERT_FAILED: {
      label: '数据插入失败',
      color: 'red',
      icon: <CloseCircleOutlined />,
    },
    WEBHOOK_PROCESSING_ERROR: {
      label: 'Webhook处理错误',
      color: 'purple',
      icon: <ExclamationCircleOutlined />,
    },
  }

  // 状态映射
  const statusMap: Record<
    string,
    { label: string; color: string; icon: React.ReactNode }
  > = {
    pending: {
      label: '待处理',
      color: 'orange',
      icon: <ExclamationCircleOutlined />,
    },
    reviewing: { label: '处理中', color: 'blue', icon: <InfoCircleOutlined /> },
    resolved: {
      label: '已解决',
      color: 'green',
      icon: <CheckCircleOutlined />,
    },
    ignored: { label: '已忽略', color: 'gray', icon: <CloseCircleOutlined /> },
  }

  // 获取记录列表
  const fetchRecords = async (page = 1, searchFilters = filters) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.pageSize.toString(),
      })

      // 添加搜索参数
      if (searchFilters.formId) params.append('formId', searchFilters.formId)
      if (searchFilters.errorType)
        params.append('errorType', searchFilters.errorType)
      if (searchFilters.status) params.append('status', searchFilters.status)
      if (searchFilters.dateRange && searchFilters.dateRange.length === 2) {
        params.append(
          'startDate',
          dayjs(searchFilters.dateRange[0]).format('YYYY-MM-DD')
        )
        params.append(
          'endDate',
          dayjs(searchFilters.dateRange[1]).format('YYYY-MM-DD')
        )
      }

      const response = await fetch(`/api/validation-failures?${params}`)
      const result: ValidationFailureListResponse = await response.json()

      if (result.success) {
        setRecords(result.data.records)
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.limit,
          total: result.data.pagination.total,
        })
      } else {
        message.error('获取验证失败记录失败')
      }
    } catch (error) {
      console.error('获取验证失败记录失败:', error)
      message.error('获取验证失败记录失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/validation-failures/stats')
      const result = await response.json()

      if (result.success) {
        setStats(result.data)
      } else {
        message.error('获取统计信息失败')
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
      message.error('获取统计信息失败')
    }
  }

  // 搜索处理
  const handleSearch = () => {
    fetchRecords(1, filters)
  }

  // 清空搜索
  const handleClearSearch = () => {
    setFilters({
      formId: '',
      errorType: '',
      status: '',
      dateRange: null,
    })
    fetchRecords(1, {
      formId: '',
      errorType: '',
      status: '',
      dateRange: null,
    })
  }

  // 查看详情
  const showDetail = (record: ValidationFailure) => {
    setSelectedRecord(record)
    setDetailVisible(true)
  }

  // 显示状态更新抽屉
  const showStatusDrawer = (record: ValidationFailure) => {
    setSelectedRecord(record)
    form.setFieldsValue({
      status: record.status,
      notes: record.notes || '',
    })
    setStatusDrawerVisible(true)
  }

  // 更新状态
  const handleUpdateStatus = async (values: any) => {
    if (!selectedRecord) return

    try {
      const response = await fetch('/api/validation-failures', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedRecord.id,
          ...values,
        }),
      })

      const result = await response.json()

      if (result.success) {
        message.success('状态更新成功')
        setStatusDrawerVisible(false)
        fetchRecords(pagination.current, filters)
        fetchStats()
      } else {
        message.error(result.error || '状态更新失败')
      }
    } catch (error) {
      console.error('状态更新失败:', error)
      message.error('状态更新失败')
    }
  }

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录')
      return
    }

    try {
      const response = await fetch('/api/validation-failures', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: selectedRowKeys,
        }),
      })

      const result = await response.json()

      if (result.success) {
        message.success(`成功删除 ${result.data.deletedCount} 条记录`)
        setSelectedRowKeys([])
        fetchRecords(pagination.current, filters)
        fetchStats()
      } else {
        message.error(result.error || '批量删除失败')
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      message.error('批量删除失败')
    }
  }

  useEffect(() => {
    fetchRecords()
    fetchStats()
  }, [])

  const columns: ColumnsType<ValidationFailure> = [
    {
      title: '时间',
      dataIndex: 'createdAt',
      width: 160,
      render: date => (
        <div>
          <div>{dayjs(date).format('MM-DD HH:mm:ss')}</div>
          <Text type="secondary" className="text-xs">
            {dayjs(date).format('YYYY')}
          </Text>
        </div>
      ),
    },
    {
      title: '表单ID',
      dataIndex: 'formId',
      width: 120,
      render: formId => <Tag color="blue">{formId}</Tag>,
    },
    {
      title: '错误类型',
      dataIndex: 'errorType',
      width: 140,
      render: errorType => {
        const typeInfo = errorTypeMap[errorType] || {
          label: errorType,
          color: 'default',
          icon: <InfoCircleOutlined />,
        }
        return (
          <Tag
            color={typeInfo.color}
            icon={typeInfo.icon}
            className="flex items-center"
          >
            {typeInfo.label}
          </Tag>
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: status => {
        const statusInfo = statusMap[status] || {
          label: status,
          color: 'default',
          icon: <InfoCircleOutlined />,
        }
        return (
          <Tag color={statusInfo.color} icon={statusInfo.icon}>
            {statusInfo.label}
          </Tag>
        )
      },
    },
    {
      title: '错误消息',
      dataIndex: 'errorMessage',
      ellipsis: true,
      render: message => (
        <Tooltip title={message}>
          <Text ellipsis style={{ maxWidth: 200 }}>
            {message}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      width: 120,
      render: ip => <Text code>{ip}</Text>,
    },
    {
      title: '处理人',
      key: 'resolver',
      width: 100,
      render: (_, record) => {
        if (!record.resolver) {
          return <Text type="secondary">-</Text>
        }
        return (
          <div>
            <div>{record.resolver.nickname}</div>
            <Text type="secondary" className="text-xs">
              @{record.resolver.username}
            </Text>
          </div>
        )
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showDetail(record)}
          />
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showStatusDrawer(record)}
          />
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-4">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-1">
            Webhook验证失败记录
          </Title>
          <Text type="secondary">管理和处理金数据Webhook验证失败的记录</Text>
        </div>
        <Space>
          <Button
            icon={<BarChartOutlined />}
            onClick={() => setStatsVisible(true)}
          >
            统计信息
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              fetchRecords(pagination.current, filters)
              fetchStats()
            }}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <Row gutter={16}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总记录数"
                value={stats.summary.total}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日新增"
                value={stats.summary.today}
                prefix={<InfoCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="待处理"
                value={stats.summary.pending}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="解决率"
                value={stats.summary.resolution_rate}
                suffix="%"
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索筛选 */}
      <Card size="small">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Input
              placeholder="表单ID"
              value={filters.formId}
              onChange={e => setFilters({ ...filters, formId: e.target.value })}
              allowClear
            />

            <Select
              placeholder="错误类型"
              value={filters.errorType}
              onChange={value => setFilters({ ...filters, errorType: value })}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(errorTypeMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value.label}
                </Option>
              ))}
            </Select>

            <Select
              placeholder="状态"
              value={filters.status}
              onChange={value => setFilters({ ...filters, status: value })}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(statusMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value.label}
                </Option>
              ))}
            </Select>

            <RangePicker
              value={filters.dateRange}
              onChange={dates => setFilters({ ...filters, dateRange: dates })}
              style={{ width: '100%' }}
            />
          </div>

          <div className="flex justify-between">
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>
              <Button icon={<ClearOutlined />} onClick={handleClearSearch}>
                重置
              </Button>
            </Space>

            {selectedRowKeys.length > 0 && (
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`}
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            )}
          </div>
        </div>
      </Card>

      {/* 记录列表 */}
      <Card>
        <Alert
          message="记录说明"
          description="系统自动记录所有Webhook验证失败的详细信息，包括原始数据、错误详情和修复建议。管理员可以查看、处理和跟踪这些问题。"
          type="info"
          showIcon
          className="mb-4"
        />

        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          columns={columns}
          dataSource={records}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              if (pageSize !== pagination.pageSize) {
                setPagination({ ...pagination, pageSize })
              }
              fetchRecords(page, filters)
            },
          }}
          scroll={{
            x: 'max-content',
            scrollToFirstRowOnChange: true
          }}
          size="small"
          className="mobile-table"
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="验证失败详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
        ]}
        width={1000}
      >
        {selectedRecord && (
          <div className="space-y-4">
            <Descriptions bordered size="small" column={2}>
              <Descriptions.Item label="失败时间" span={2}>
                {dayjs(selectedRecord.createdAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="表单ID" span={1}>
                <Tag color="blue">{selectedRecord.formId}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="错误类型" span={1}>
                <Tag
                  color={
                    errorTypeMap[selectedRecord.errorType]?.color || 'default'
                  }
                  icon={errorTypeMap[selectedRecord.errorType]?.icon}
                >
                  {errorTypeMap[selectedRecord.errorType]?.label ||
                    selectedRecord.errorType}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态" span={1}>
                <Tag
                  color={statusMap[selectedRecord.status]?.color || 'default'}
                  icon={statusMap[selectedRecord.status]?.icon}
                >
                  {statusMap[selectedRecord.status]?.label ||
                    selectedRecord.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="IP地址" span={1}>
                <Text code>{selectedRecord.ipAddress}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="错误消息" span={2}>
                {selectedRecord.errorMessage}
              </Descriptions.Item>
              {selectedRecord.resolver && (
                <Descriptions.Item label="处理人" span={1}>
                  {selectedRecord.resolver.nickname} (@
                  {selectedRecord.resolver.username})
                </Descriptions.Item>
              )}
              {selectedRecord.resolvedAt && (
                <Descriptions.Item label="处理时间" span={1}>
                  {dayjs(selectedRecord.resolvedAt).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )}
                </Descriptions.Item>
              )}
              {selectedRecord.notes && (
                <Descriptions.Item label="处理备注" span={2}>
                  {selectedRecord.notes}
                </Descriptions.Item>
              )}
            </Descriptions>

            {/* 原始数据 */}
            <div>
              <Title level={5}>原始数据</Title>
              <pre className="bg-gray-50 p-4 rounded text-sm overflow-auto max-h-64">
                {JSON.stringify(selectedRecord.originalData, null, 2)}
              </pre>
            </div>

            {/* 验证错误详情 */}
            <div>
              <Title level={5}>验证错误详情</Title>
              <pre className="bg-red-50 p-4 rounded text-sm overflow-auto max-h-64">
                {JSON.stringify(selectedRecord.validationErrors, null, 2)}
              </pre>
            </div>

            {/* 期望格式 */}
            {selectedRecord.expectedFormat && (
              <div>
                <Title level={5}>期望格式</Title>
                <pre className="bg-blue-50 p-4 rounded text-sm overflow-auto max-h-64">
                  {JSON.stringify(selectedRecord.expectedFormat, null, 2)}
                </pre>
              </div>
            )}

            {/* 修复建议 */}
            {selectedRecord.suggestions && (
              <div>
                <Title level={5}>修复建议</Title>
                <pre className="bg-green-50 p-4 rounded text-sm overflow-auto max-h-64">
                  {JSON.stringify(selectedRecord.suggestions, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 状态更新抽屉 */}
      <Drawer
        title="更新处理状态"
        placement="right"
        width={400}
        open={statusDrawerVisible}
        onClose={() => setStatusDrawerVisible(false)}
      >
        <Form form={form} layout="vertical" onFinish={handleUpdateStatus}>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              {Object.entries(statusMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  <Tag color={value.color} icon={value.icon}>
                    {value.label}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="notes" label="处理备注">
            <TextArea rows={4} placeholder="请输入处理备注..." />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                更新状态
              </Button>
              <Button onClick={() => setStatusDrawerVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Drawer>

      {/* 统计信息模态框 */}
      <Modal
        title="统计信息"
        open={statsVisible}
        onCancel={() => setStatsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setStatsVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {stats && (
          <div className="space-y-6">
            {/* 概要统计 */}
            <Card title="概要统计" size="small">
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic title="总记录数" value={stats.summary.total} />
                </Col>
                <Col span={8}>
                  <Statistic title="待处理" value={stats.summary.pending} />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="解决率"
                    value={stats.summary.resolution_rate}
                    suffix="%"
                  />
                </Col>
              </Row>
            </Card>

            {/* 错误类型分布 */}
            <Card title="错误类型分布" size="small">
              <div className="space-y-2">
                {stats.errorTypeStats.map(stat => (
                  <div
                    key={stat.errorType}
                    className="flex justify-between items-center"
                  >
                    <Tag
                      color={errorTypeMap[stat.errorType]?.color || 'default'}
                    >
                      {errorTypeMap[stat.errorType]?.label || stat.errorType}
                    </Tag>
                    <div>
                      <Text strong>{stat.count}</Text>
                      <Text type="secondary" className="ml-2">
                        ({stat.percentage}%)
                      </Text>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* 表单分布 */}
            <Card title="表单分布 (Top 10)" size="small">
              <div className="space-y-2">
                {stats.formIdStats.map(stat => (
                  <div
                    key={stat.formId}
                    className="flex justify-between items-center"
                  >
                    <Tag color="blue">{stat.formId}</Tag>
                    <div>
                      <Text strong>{stat.count}</Text>
                      <Text type="secondary" className="ml-2">
                        ({stat.percentage}%)
                      </Text>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* 处理时间统计 */}
            {stats.resolutionTime.avg_hours !== null && (
              <Card title="处理时间统计" size="small">
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="平均处理时间"
                      value={stats.resolutionTime.avg_hours?.toFixed(1)}
                      suffix="小时"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="最快处理时间"
                      value={stats.resolutionTime.min_hours || 0}
                      suffix="小时"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="最慢处理时间"
                      value={stats.resolutionTime.max_hours || 0}
                      suffix="小时"
                    />
                  </Col>
                </Row>
              </Card>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}
