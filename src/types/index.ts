// 通用类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: string
  timestamp: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 用户相关类型
export interface User {
  id: number
  username: string
  nickname?: string
  email?: string
  avatarUrl?: string
  isActive: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

// 表单配置相关类型
export interface FormConfig {
  id: number
  formId: string
  formName: string
  fieldMapping: Record<string, FieldMapping>
  tableName?: string
  tableCreated: boolean
  webhookUrl?: string
  isActive: boolean
  createdById?: number
  createdAt: string
  updatedAt: string
}

export interface FieldMapping {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'text'
  required?: boolean
  enabled?: boolean // 新增：字段是否启用
  defaultValue?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    enum?: string[]
  }
}

// 金数据Webhook数据类型
export interface JinshujuWebhookData {
  form: string
  form_name: string
  entry: {
    serial_number: number
    [key: string]: any
    // 元数据字段
    color_mark?: string
    creator_name?: string
    created_at: string
    updated_at: string
    info_filling_duration?: number
    info_platform?: string
    info_os?: string
    info_browser?: string
    info_region?: {
      province?: string
      city?: string
      district?: string
      street?: string
    }
    info_remote_ip?: string
  }
}

// 表单数据类型
export interface FormData {
  id: number
  formId: string
  serialNumber?: number
  rawData: JinshujuWebhookData
  [key: string]: any // 动态字段
  createdAt: string
  updatedAt: string
}

// 系统日志类型
export interface SystemLog {
  id: number
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  context?: Record<string, any>
  userId?: number
  ipAddress?: string
  userAgent?: string
  createdAt: string
  user?: User
}

// 数据表格相关类型
export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex?: string
  width?: number
  fixed?: 'left' | 'right'
  sorter?: boolean
  searchable?: boolean
  render?: (value: any, record: T, index: number) => React.ReactNode
  filters?: Array<{ text: string; value: any }>
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
    showSizeChanger?: boolean
    showQuickJumper?: boolean
    onChange?: (page: number, pageSize: number) => void
  }
  rowSelection?: {
    selectedRowKeys: React.Key[]
    onChange: (selectedRowKeys: React.Key[], selectedRows: T[]) => void
  }
  onRefresh?: () => void
}

// 筛选器类型
export interface FilterOption {
  label: string
  value: any
  count?: number
}

export interface Filter {
  key: string
  label: string
  type: 'select' | 'dateRange' | 'search' | 'multiSelect'
  options?: FilterOption[]
  placeholder?: string
}

// 导出相关类型
export interface ExportConfig {
  format: 'xlsx' | 'csv' | 'pdf'
  filename?: string
  fields?: string[]
  filters?: Record<string, any>
  includeHeaders?: boolean
}

// 文件上传类型
export interface UploadFile {
  uid: string
  name: string
  size: number
  type: string
  url?: string
  status?: 'uploading' | 'done' | 'error' | 'removed'
  percent?: number
  response?: any
  error?: any
}

// 菜单项类型
export interface MenuItem {
  key: string
  label: React.ReactNode
  icon?: React.ReactNode
  children?: MenuItem[]
  disabled?: boolean
  hidden?: boolean
}

// 面包屑类型
export interface BreadcrumbItem {
  title: React.ReactNode
  href?: string
  key?: string
}

// 主题配置类型
export interface ThemeConfig {
  primaryColor: string
  componentSize: 'small' | 'middle' | 'large'
  borderRadius: number
  wireframe: boolean
}

// 状态管理相关类型
export interface LoadingState {
  [key: string]: boolean
}

export interface ErrorState {
  [key: string]: string | null
}

// 表单验证类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => Promise<void> | void
  message?: string
}

export interface FormField {
  name: string
  label: string
  type:
    | 'input'
    | 'password'
    | 'textarea'
    | 'select'
    | 'checkbox'
    | 'radio'
    | 'date'
    | 'upload'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: ValidationRule[]
  disabled?: boolean
  hidden?: boolean
  defaultValue?: any
}

// 统计数据类型
export interface StatCard {
  title: string
  value: number | string
  icon?: React.ReactNode
  color?: string
  trend?: {
    value: number
    isUp: boolean
  }
  formatter?: (value: number) => string
}

// 图表数据类型
export interface ChartData {
  name: string
  value: number
  color?: string
}

// 通知类型
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  createdAt: string
  actionUrl?: string
}

// 设置类型
export interface Settings {
  appearance: {
    theme: 'light' | 'dark'
    primaryColor: string
    componentSize: 'small' | 'middle' | 'large'
  }
  notification: {
    email: boolean
    push: boolean
    sound: boolean
  }
  privacy: {
    profileVisible: boolean
    activityTracking: boolean
  }
}

// HTTP相关类型
export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  url: string
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  timeout?: number
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: string
  path?: string
}

// 组件Props类型
export interface BaseComponentProps {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}

// 路由相关类型
export interface RouteConfig {
  path: string
  component: React.ComponentType
  exact?: boolean
  title?: string
  requireAuth?: boolean
  permissions?: string[]
}
