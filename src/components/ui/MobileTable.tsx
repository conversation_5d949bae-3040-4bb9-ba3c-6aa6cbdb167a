'use client'

import { Table, TableProps } from 'antd'
import { useEffect, useRef, useState } from 'react'

interface MobileTableProps extends TableProps<any> {
  showScrollHint?: boolean
}

export function MobileTable({ 
  showScrollHint = true, 
  className = '', 
  ...props 
}: MobileTableProps) {
  const tableRef = useRef<HTMLDivElement>(null)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isScrolledToRight, setIsScrolledToRight] = useState(false)

  useEffect(() => {
    const tableWrapper = tableRef.current?.querySelector('.ant-table-wrapper')
    if (!tableWrapper) return

    const handleScroll = () => {
      const { scrollLeft, scrollWidth, clientWidth } = tableWrapper
      setIsScrolled(scrollLeft > 0)
      setIsScrolledToRight(scrollLeft >= scrollWidth - clientWidth - 1)
    }

    // 初始检查
    handleScroll()

    tableWrapper.addEventListener('scroll', handleScroll)
    return () => tableWrapper.removeEventListener('scroll', handleScroll)
  }, [])

  const tableClassName = [
    'mobile-table',
    isScrolled ? 'scrolled' : '',
    isScrolledToRight ? 'scrolled-to-right' : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <div ref={tableRef}>
      <Table
        {...props}
        className={tableClassName}
        scroll={{
          x: 'max-content',
          scrollToFirstRowOnChange: true,
          ...props.scroll
        }}
      />
      {showScrollHint && (
        <div className="mobile-table-hint block md:hidden" />
      )}
    </div>
  )
}
